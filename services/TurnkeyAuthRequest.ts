import config from "@/config";
import BaseRequest from "./BaseRequest";
import { TurnkeyLoginType } from "@/types/turnkey.type";

export default class TurnkeyAuthRequest extends BaseRequest {
  getUrlPrefix() {
    return "http://172.16.198.43:11000/api/v1";
  }

  async exchangeTurnkeySession(body: TurnkeyLoginType) {
    const url = `/turnkey/login`;

    // Validate required fields
    if (!body.oauthProvider?.oidcToken) {
      throw new Error("Missing OAuth provider token");
    }
    if (!body.oauthProvider?.providerName) {
      throw new Error("Missing OAuth provider name");
    }

    // Validate and log public key format if present
    if (body.publicKey) {
      const keyBytes = new Uint8Array(
        body.publicKey.match(/.{1,2}/g)?.map((byte) => parseInt(byte, 16)) || []
      );

      console.log("Turnkey login request:", {
        provider: body.oauthProvider.providerName,
        hasToken: !!body.oauthProvider.oidcToken,
        hasPublicKey: !!body.publicKey,
        hasEmail: !!body.email,
        publicKeyLength: body.publicKey.length,
        publicKeyBytes: keyBytes.length,
        isUncompressed: keyBytes.length === 65 && keyBytes[0] === 0x04,
        publicKeyPrefix: body.publicKey.substring(0, 6),
      });

      // Warn if the key doesn't look like uncompressed P256
      if (keyBytes.length !== 65 || keyBytes[0] !== 0x04) {
        console.warn(
          "Public key may not be in expected uncompressed P256 format:",
          {
            expectedBytes: 65,
            actualBytes: keyBytes.length,
            expectedPrefix: "04",
            actualPrefix: keyBytes[0]?.toString(16).padStart(2, "0"),
          }
        );
      }
    } else {
      console.log("Turnkey login request:", {
        provider: body.oauthProvider.providerName,
        hasToken: !!body.oauthProvider.oidcToken,
        hasPublicKey: false,
        hasEmail: !!body.email,
      });
    }

    return this.post(url, body);
  }
}

import config from "@/config";
import BaseRequest from "./BaseRequest";
import { TurnkeyLoginType } from "@/types/turnkey.type";

export default class TurnkeyAuthRequest extends BaseRequest {
  getUrlPrefix() {
    return "https://api-turnkey.raidenx.io/api/v1";
  }

  async exchangeTurnkeySession(body: TurnkeyLoginType) {
    const url = `/turnkey/login`;

    // Validate required fields
    if (!body.oauthProvider?.oidcToken) {
      throw new Error("Missing OAuth provider token");
    }
    if (!body.oauthProvider?.providerName) {
      throw new Error("Missing OAuth provider name");
    }

    // Log for debugging (without sensitive data)
    console.log("Turnkey login request:", {
      provider: body.oauthProvider.providerName,
      hasToken: !!body.oauthProvider.oidcToken,
      hasPublicKey: !!body.publicKey,
      hasEmail: !!body.email,
    });

    return this.post(url, body);
  }
}

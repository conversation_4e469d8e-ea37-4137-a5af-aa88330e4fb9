"use client";

import { useSearchParams } from "next/navigation";
import { AppButton } from "@/components/AppButton";
import { useRouter } from "next/navigation";

export default function AuthError() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");
  const router = useRouter();

  const getErrorMessage = (error: string | null) => {
    switch (error) {
      case "MissingNonce":
        return "Authentication failed: Missing security nonce";
      case "InvalidNonce":
        return "Authentication failed: Invalid security nonce";
      case "MissingCode":
        return "Authentication failed: Missing authorization code";
      case "AuthFailed":
        return "Authentication failed: Please try again";
      default:
        return "Authentication failed: Unknown error";
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-md rounded-lg bg-gray-900 p-6 text-center">
        <h1 className="mb-4 text-xl font-semibold text-red-400">
          Authentication Error
        </h1>
        <p className="mb-6 text-gray-300">{getErrorMessage(error)}</p>
        <AppButton
          onClick={() => router.push("/")}
          variant="primary"
          size="large"
          className="w-full"
        >
          Return Home
        </AppButton>
      </div>
    </div>
  );
}
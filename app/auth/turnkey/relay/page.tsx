"use client";

import { useEffect, useState } from "react";

/**
 * @deprecated This relay page is no longer used in the authentication flow.
 *
 * As of the latest update, Google OAuth authentication redirects directly to /new-pairs
 * instead of using this relay page. All authentication processing (JWT storage, Redux
 * state updates, socket reconnection, etc.) is now handled by the AuthFinalizer component
 * which runs automatically on all pages.
 *
 * This page is kept for backward compatibility but should not be used in new flows.
 * Consider removing this page in a future cleanup.
 */
export default function RelayPage() {
  const [error, setError] = useState<string>("");

  useEffect(() => {
    // This page is deprecated - redirect to new pairs page
    console.warn("Relay page is deprecated. Redirecting to /new-pairs");
    setError("This page is deprecated. Redirecting...");

    // Redirect after a short delay to show the message
    const timer = setTimeout(() => {
      window.location.href = "/new-pairs";
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="flex min-h-[60vh] items-center justify-center px-6">
      <div className="text-center">
        <div className="text-white-1000 mb-2 text-lg font-semibold">
          Page Deprecated
        </div>
        <div className="text-white-500 text-sm">
          {error ||
            "This authentication relay page is no longer used. Redirecting to new pairs page..."}
        </div>
      </div>
    </div>
  );
}

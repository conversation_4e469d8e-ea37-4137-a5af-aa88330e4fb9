import { TPair } from "@/types";
import { Metadata } from "next";
import { headers } from "next/headers";
import config from "@/config";
import { RootPairProvider } from "@/app/sui/(token_detail)/provider";
import { formatNumberWithSubscriptZeros } from "@/utils/format";
import { normalizeStructTag } from "@mysten/sui/utils";

export const revalidate = 15;

interface LayoutProps {
  desktop: React.ReactNode;
  mobile: React.ReactNode;
  params: Promise<{ tokenAddress: string }>;
}

async function getTokenDetail(tokenAddress: string): Promise<TPair | null> {
  try {
    let standardTokenAddress = tokenAddress;
    try {
      standardTokenAddress = normalizeStructTag(
        decodeURIComponent(tokenAddress)
      );
    } catch (error) {
      console.warn(
        `standardTokenAddress ${decodeURIComponent(
          tokenAddress
        )} throw error: ${error}, use original token address`
      );
    }
    const url = `${config.apiUrl}/sui/tokens/${standardTokenAddress}/top-pair`;
    const res = await fetch(url, {
      next: { revalidate: 5 }, // Clear cache after 5 seconds
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!res.ok) {
      console.error(
        `Failed to fetch token detail: ${res.status} ${res.statusText}`
      );
      return null;
    }

    const pair: TPair = await res.json();
    return pair;
  } catch (error) {
    console.error("Error fetching token detail:", error);
    return null;
  }
}

export async function generateMetadata({
  params,
}: LayoutProps): Promise<Metadata> {
  const { tokenAddress } = await params;
  const pair = await getTokenDetail(tokenAddress);

  const description = `Fastest trading & sniping bot on SUI, 1-click buy & sell, realtime chart, audit results, limit orders, DCA, copy trade - only on RaidenX`;

  // Fallback metadata if pair data is not available
  const defaultTitle = "RaidenX | The best trading terminal on SUI";
  const title = pair
    ? `${pair.tokenBase?.symbol?.toUpperCase()} $${formatNumberWithSubscriptZeros(
        pair.tokenBase?.priceUsd || "0"
      )} ${pair.tokenBase?.name} / ${pair.tokenQuote?.symbol} on ${
        pair.dex?.name
      } - RaidenX`
    : defaultTitle;
  return {
    metadataBase: new URL(config.appUrl),
    title,
    description,
    openGraph: {
      title,
      description,
      images: [
        pair?.tokenBase?.bannerImageUrl ||
          pair?.tokenBase?.logoImageUrl ||
          "/open-graph.png",
      ],
    },
    keywords: [
      "RaidenX",
      "RaidenXTradingTerminal",
      "RaidenXTradingBot",
      "RaidenXTGBot",
      "RaidenXTelegramBot",
      "RaidenXSniperBot",
      "SuiTradingTerminal",
      "SuiTradingBot",
      "SuiMemeCoin",
      "SuiTGBot",
      "SuiTelegramBot",
      "SuiSniperBot",
      "SuiCopyTrade",
    ],
  };
}

export default async function PairLayout({
  desktop,
  mobile,
  params,
}: LayoutProps) {
  const headersList = await headers();
  const isMobile = (headersList.get("user-agent") || "")?.includes("Mobile");

  const { tokenAddress } = await params;
  const pair = await getTokenDetail(tokenAddress);

  return (
    <RootPairProvider externalPair={pair}>
      {isMobile ? mobile : desktop}
    </RootPairProvider>
  );
}

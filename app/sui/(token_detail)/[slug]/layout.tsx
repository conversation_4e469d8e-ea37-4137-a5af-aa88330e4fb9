import { headers } from "next/headers";
import { RootPairProvider } from "../provider";
import config from "@/config";
import { TPair } from "@/types";
import { Metadata } from "next";
import { formatNumberWithSubscriptZeros } from "@/utils/format";
export const revalidate = 15;

interface LayoutProps {
  desktop: React.ReactNode;
  mobile: React.ReactNode;
  params: Promise<{ slug: string }>;
}

async function getTokenDetail(slug: string): Promise<TPair | null> {
  try {
    const url = `${config.apiUrl}/sui/pairs/${decodeURIComponent(slug)}`;
    const res = await fetch(url, {
      next: { revalidate: 5 }, // Clear cache after 5 seconds
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!res.ok) {
      console.error(
        `Failed to fetch token detail: ${res.status} ${res.statusText}`
      );
      return null;
    }

    const pair: TPair = await res.json();
    return pair;
  } catch (error) {
    console.error("Error fetching token detail:", error);
    return null;
  }
}

export async function generateMetadata({
  params,
}: LayoutProps): Promise<Metadata> {
  const { slug } = await params;
  const pair = await getTokenDetail(slug);

  const description = `Fastest trading & sniping bot on SUI, 1-click buy & sell, realtime chart, audit results, limit orders, DCA, copy trade - only on RaidenX`;

  // Fallback metadata if pair data is not available
  const defaultTitle = "RaidenX | The best trading terminal on SUI";
  const title = pair
    ? `${pair.tokenBase?.symbol?.toUpperCase()} $${formatNumberWithSubscriptZeros(
        pair.tokenBase?.priceUsd
      )} ${pair.tokenBase?.name} / ${pair.tokenQuote?.symbol} on ${
        pair.dex?.name
      } - RaidenX | The best trading terminal on SUI`
    : defaultTitle;

  const imageUrl =
    pair?.tokenBase?.bannerImageUrl ||
    pair?.tokenBase?.logoImageUrl ||
    "/open-graph.png";
  return {
    title,
    description,
    icons: {
      icon: "/favicon.ico",
      apple: "/logo192.png",
    },
    openGraph: {
      title,
      description,
      images: [imageUrl],
      url: `https://raidenx.io/sui/${slug}`,
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [imageUrl],
    },
    other: {
      "msapplication-TileColor": "#da532c",
      canonical: `https://raidenx.io/sui/${slug}`,
    },
    keywords: [
      "RaidenX",
      "RaidenXTradingTerminal",
      "RaidenXTradingBot",
      "RaidenXTGBot",
      "RaidenXTelegramBot",
      "RaidenXSniperBot",
      "SuiTradingTerminal",
      "SuiTradingBot",
      "SuiMemeCoin",
      "SuiTGBot",
      "SuiTelegramBot",
      "SuiSniperBot",
      "SuiCopyTrade",
    ],
  };
}

export default async function Layout({ desktop, mobile, params }: LayoutProps) {
  const headersList = await headers();
  const isMobile = (headersList.get("user-agent") || "")?.includes("Mobile");
  const { slug } = await params;
  const pair = await getTokenDetail(slug);

  // If pair is null, we'll let RootPairProvider handle it
  // The provider should be updated to handle null externalPair gracefully
  return (
    <RootPairProvider externalPair={pair}>
      {isMobile ? mobile : desktop}
    </RootPairProvider>
  );
}

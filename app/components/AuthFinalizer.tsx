"use client";

import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { setUserAuth, setIsExternalWallet } from "@/store/user.store";
import Storage from "@/libs/storage";
import { createSocketInstance, closeSocketInstance } from "@/libs/socket";
import { NETWORKS } from "@/utils/contants";
import { useCurrentAccount, useDisconnectWallet } from "@mysten/dapp-kit";

export default function AuthFinalizer() {
  const dispatch = useDispatch<AppDispatch>();
  const [finalizedForToken, setFinalizedForToken] = useState<string | null>(
    null
  );
  const currentAccount = useCurrentAccount();
  const { mutate: disconnect } = useDisconnectWallet();

  useEffect(() => {
    const run = async () => {
      try {
        const res = await fetch("/api/auth/session-jwt", { cache: "no-store" });
        if (!res.ok) return;
        const { jwtToken } = await res.json();
        if (!jwtToken || finalizedForToken === jwtToken) return;

        dispatch(setUserAuth({ accessToken: jwtToken }));
        if (currentAccount?.address) disconnect();
        Storage.setLoginMethod("turnkey");
        dispatch(setIsExternalWallet(false));

        const redirectAfterLogin = Storage.getRedirectAfterLogin();
        if (redirectAfterLogin) {
          const location = `${window.location.pathname}${window.location.search}`;
          if (location !== redirectAfterLogin) {
            Storage.clearRedirectAfterLogin();
            window.location.href = redirectAfterLogin;
          }
        }

        // Reconnect sockets with new token
        try {
          closeSocketInstance(NETWORKS.SUI);
          createSocketInstance(NETWORKS.SUI, jwtToken);
        } catch {}

        try {
          await fetch("/api/auth/session-jwt", { method: "DELETE" });
        } catch {}

        setFinalizedForToken(jwtToken);
      } catch {}
    };

    run();
  }, [dispatch, finalizedForToken, currentAccount?.address, disconnect]);

  return null;
}

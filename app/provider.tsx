"use client";

import { store } from "@/store";
import { ReactNode, useCallback, useEffect, Suspense } from "react";
import { Provider } from "react-redux";
import { WalletProvider, SocketProvider, MetadataProvider } from "@/context";
import { SessionProvider } from "next-auth/react";
import Storage from "@/libs/storage";
import {
  setPermissions,
  setUserAuth,
  getFavouritePairs,
  getReferralMyCode,
  getSettingsOrder,
} from "@/store/user.store";
import rf from "@/services/RequestFactory";
import { NETWORKS } from "@/utils/contants";
import { useSearchParams } from "next/navigation";
import { setIsShowModalEnterCode } from "@/store/metadata.store";
import WalletExternalProvider from "./WalletProvider";
import React, { lazy } from "react";
const AuthFinalizer = lazy(() => import("@/app/components/AuthFinalizer"));

const SearchParamsHandler = () => {
  const searchParams = useSearchParams();

  useEffect(() => {
    if (searchParams.get("code")) {
      const code = searchParams.get("code");
      if (code) {
        store.dispatch(setIsShowModalEnterCode({ isShow: true }));
      }
    }
  }, [searchParams]);

  return null;
};

const extractReferralCodeFromQueryUrl = () => {
  if (typeof window === "undefined") {
    return null;
  }
  const urlParams = new URLSearchParams(window.location.search);
  const refCode = urlParams.get("ref");
  return refCode;
};

export const AppProvider = ({ children }: { children: ReactNode }) => {
  const accessToken = Storage.getAccessToken();
  if (accessToken) {
    store.dispatch(setUserAuth({ accessToken }));
    const redirectAfterLogin = Storage.getRedirectAfterLogin();
    if (redirectAfterLogin && typeof window !== "undefined") {
      const location = `${window.location.pathname}${window.location.search}`;
      if (location !== redirectAfterLogin) {
        Storage.clearRedirectAfterLogin();
        window.location.href = redirectAfterLogin;
      }
    }
  }

  const referralCode = extractReferralCodeFromQueryUrl();
  if (referralCode && !Storage.getReferralCode()) {
    Storage.setReferralCode(referralCode);
  }

  const getPermissions = useCallback(async () => {
    try {
      const permissions = await rf.getRequest("OauthRequest").getPermissions();
      store.dispatch(setPermissions(permissions));
    } catch (error: any) {
      console.error(`Failed to get permissions:`, error?.message);
    }
  }, []);

  useEffect(() => {
    if (!accessToken) return;
    getPermissions().then();
    store.dispatch(getSettingsOrder({ network: NETWORKS.SUI }));
    store.dispatch(getFavouritePairs());
    store.dispatch(getReferralMyCode());
  }, [accessToken]);

  return (
    <Provider store={store}>
      <SessionProvider>
        <WalletExternalProvider>
          <SocketProvider accessToken={accessToken}>
            <MetadataProvider>
              <WalletProvider>
                <Suspense fallback={null}>
                  <SearchParamsHandler />
                </Suspense>
                <Suspense fallback={null}>
                  {typeof window !== "undefined" && <AuthFinalizer />}
                </Suspense>
                {children}
              </WalletProvider>
            </MetadataProvider>
          </SocketProvider>
        </WalletExternalProvider>
      </SessionProvider>
    </Provider>
  );
};

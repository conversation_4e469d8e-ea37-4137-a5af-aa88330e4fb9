import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const nonce = searchParams.get("nonce");
  
  if (!nonce) {
    return NextResponse.redirect(new URL("/auth/error?error=MissingNonce", request.url));
  }

  // Store nonce in cookie for callback validation
  const cookieStore = await cookies();
  cookieStore.set("turnkey_login_nonce", nonce, {
    httpOnly: true,
    secure: true,
    sameSite: "lax",
    path: "/",
    maxAge: 600, // 10 minutes
  });

  // Build Google OAuth URL with custom nonce
  const googleAuthUrl = new URL("https://accounts.google.com/o/oauth2/v2/auth");
  googleAuthUrl.searchParams.set("client_id", process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!);
  googleAuthUrl.searchParams.set("redirect_uri", `${process.env.NEXTAUTH_URL}/api/auth/google/callback`);
  googleAuthUrl.searchParams.set("response_type", "code");
  googleAuthUrl.searchParams.set("scope", "openid email profile");
  googleAuthUrl.searchParams.set("nonce", nonce);
  googleAuthUrl.searchParams.set("state", crypto.randomUUID());

  return NextResponse.redirect(googleAuthUrl.toString());
}
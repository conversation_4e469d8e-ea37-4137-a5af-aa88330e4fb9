"use client";

import React, { useState, useEffect } from "react";
import { TurnkeyProvider } from "@turnkey/sdk-react";

export default function TurnkeyProviderWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isClient, setIsClient] = useState(false);
  const [config, setConfig] = useState<{
    apiBaseUrl: string;
    defaultOrganizationId: string;
  } | null>(null);

  useEffect(() => {
    // Only set config on client side to avoid hydration mismatch
    setConfig({
      apiBaseUrl: process.env.NEXT_PUBLIC_BASE_URL || "https://api.turnkey.com",
      defaultOrganizationId:
        process.env.NEXT_PUBLIC_ORGANIZATION_ID ||
        "848a3588-fe7d-4286-a0e4-1ca21d906429",
    });
    setIsClient(true);
  }, []);

  // Render children without TurnkeyProvider during SSR
  if (!isClient || !config) {
    return <>{children}</>;
  }

  return <TurnkeyProvider config={config}>{children}</TurnkeyProvider>;
}

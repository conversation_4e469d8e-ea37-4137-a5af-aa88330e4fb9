"use client";

import { useCallback } from "react";
import { useTurnkey } from "@turnkey/sdk-react";
import { createHash } from "crypto";

/**
 * Derive a stable nonce from Turnkey IndexedDB public key.
 * - Fetches a short-lived session token from our API
 * - Initializes IndexedDB client and logs in with that session if supported
 * - Fetches public key
 * - Returns a base64url(SHA-256(publicKey)) string
 */
export function useTurnkeyNonce() {
  const { indexedDbClient } = useTurnkey();

  const getNonce = useCallback(async (): Promise<string> => {
    if (!indexedDbClient) throw new Error("Turnkey not ready");

    // 1) Get a short-lived session token for initializing the client
    let sessionToken: string | undefined;
    try {
      const res = await fetch("/api/turnkey/session", { cache: "no-store" });
      if (res.ok) {
        const data = await res.json();
        sessionToken = data?.jwtToken;
      }
    } catch {}

    await indexedDbClient.init();
    try {
      if (
        sessionToken &&
        typeof (indexedDbClient as any).loginWithSession === "function"
      ) {
        await (indexedDbClient as any).loginWithSession(sessionToken);
      }
    } catch {}

    const publicKey = (await indexedDbClient.getPublicKey()) || "";
    const nonce = createHash("sha256").update(publicKey).digest("hex");
    return nonce;
  }, [indexedDbClient]);

  /**
   * Get both the nonce and the raw public key for authentication
   */
  const getNonceAndPublicKey = useCallback(async (): Promise<{
    nonce: string;
    publicKey: string;
  }> => {
    if (!indexedDbClient) throw new Error("Turnkey not ready");

    // 1) Get a short-lived session token for initializing the client
    let sessionToken: string | undefined;
    try {
      const res = await fetch("/api/turnkey/session", { cache: "no-store" });
      if (res.ok) {
        const data = await res.json();
        sessionToken = data?.jwtToken;
      }
    } catch {}

    await indexedDbClient.init();
    try {
      if (
        sessionToken &&
        typeof (indexedDbClient as any).loginWithSession === "function"
      ) {
        await (indexedDbClient as any).loginWithSession(sessionToken);
      }
    } catch {}

    const publicKey = (await indexedDbClient.getPublicKey()) || "";
    if (!publicKey) {
      throw new Error("Failed to retrieve Turnkey public key");
    }

    const nonce = createHash("sha256").update(publicKey).digest("hex");
    return { nonce, publicKey };
  }, [indexedDbClient]);

  return { getNonce, getNonceAndPublicKey };
}

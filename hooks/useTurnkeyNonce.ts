"use client";

import { useCallback } from "react";
import { useTurnkey } from "@turnkey/sdk-react";
import { createHash } from "crypto";
import { ec as EC } from "elliptic";

export function useTurnkeyNonce() {
  const { indexedDbClient } = useTurnkey();

  const getNonce = useCallback(async (): Promise<string> => {
    if (!indexedDbClient) throw new Error("Turnkey not ready");

    // 1) Get a short-lived session token for initializing the client
    let sessionToken: string | undefined;
    try {
      const res = await fetch("/api/turnkey/session", { cache: "no-store" });
      if (res.ok) {
        const data = await res.json();
        sessionToken = data?.jwtToken;
      }
    } catch {}

    await indexedDbClient.init();
    try {
      if (
        sessionToken &&
        typeof (indexedDbClient as any).loginWithSession === "function"
      ) {
        await (indexedDbClient as any).loginWithSession(sessionToken);
      }
    } catch {}

    const publicKey = (await indexedDbClient.getPublicKey()) || "";

    const nonce = createHash("sha256").update(publicKey).digest("hex");
    return nonce;
  }, [indexedDbClient]);

  const getNonceAndPublicKey = useCallback(async (): Promise<{
    nonce: string;
    publicKey: string;
  }> => {
    if (!indexedDbClient) throw new Error("Turnkey not ready");

    // ... existing session token logic ...

    await indexedDbClient.init();

    // ... existing session login logic ...

    const compressedPublicKey = (await indexedDbClient.getPublicKey()) || "";

    const ec = new EC("p256");
    const keyPair = ec.keyFromPublic(compressedPublicKey, "hex");
    const uncompressedPublicKey = keyPair.getPublic("hex");

    const nonce = createHash("sha256")
      .update(uncompressedPublicKey)
      .digest("hex");
    return { nonce, publicKey: uncompressedPublicKey };
  }, [indexedDbClient]);

  return { getNonce, getNonceAndPublicKey };
}
